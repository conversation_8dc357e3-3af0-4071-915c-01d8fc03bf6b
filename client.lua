local inZone = false
local props = {}
local interactedProps = {}
local isCollecting = false

-- Vytvoření z<PERSON>y pomocí ox_lib
lib.zones.sphere({
    coords = Config.Zone.coords,
    radius = Config.Zone.radius,
    debug = false,
    onEnter = function()
        inZone = true
        TriggerEvent('redwood_tobacco:spawnProps')
    end,
    onExit = function()
        inZone = false
        TriggerEvent('redwood_tobacco:removeProps')
    end
})

-- Spawn propů
RegisterNetEvent('redwood_tobacco:spawnProps')
AddEventHandler('redwood_tobacco:spawnProps', function()
    if not inZone then return end

    for _, coords in ipairs(Config.Prop.locations) do
        local prop = CreateObject(GetHashKey(Config.Prop.model), coords.x, coords.y, coords.z, false, false, false)
        PlaceObjectOnGroundProperly(prop)
        FreezeEntityPosition(prop, true)
        table.insert(props, prop)

        -- Interakce s propem pomocí ox_lib
        exports.ox_target:addLocalEntity(prop, {
            {
                name = 'collect_tobacco',
                label = 'Sebrat tabák',
                icon = 'fas fa-leaf',
                distance = 2.0,
                canInteract = function()
                    return inZone and not isCollecting and not interactedProps[prop]
                end,
                onSelect = function()
                    if not ESX.PlayerData.job or ESX.PlayerData.job.name ~= Config.Job then
                        lib.notify({ title = 'Chyba', description = 'Musíš být zaměstnán u Redwood!', type = 'error' })
                        return
                    end

                    TriggerServerEvent('redwood_tobacco:checkItemAndCollect', prop)
                end
            }
        })
    end
end)

-- Odstranění propů
RegisterNetEvent('redwood_tobacco:removeProps')
AddEventHandler('redwood_tobacco:removeProps', function()
    for _, prop in ipairs(props) do
        if DoesEntityExist(prop) then
            exports.ox_target:removeLocalEntity(prop, 'collect_tobacco')
            DeleteObject(prop)
        end
    end
    props = {}
end)

-- Animace sběru
RegisterNetEvent('redwood_tobacco:startCollecting')
AddEventHandler('redwood_tobacco:startCollecting', function(prop)
    isCollecting = true
    local playerPed = PlayerPedId()

    -- Animace kopání
    RequestAnimDict('melee@large_wpn@streamed_core')
    while not HasAnimDictLoaded('melee@large_wpn@streamed_core') do
        Citizen.Wait(100)
    end
    TaskPlayAnim(playerPed, 'melee@large_wpn@streamed_core', 'ground_attack_on_spot', 8.0, -8.0, 5000, 0, 0, false, false, false)

    -- Progress bar
    lib.progressBar({
        duration = 5000,
        label = 'Sbíráš tabák...',
        useWhileDead = false,
        canCancel = true,
        disable = { move = true, car = true, combat = true }
    })

    -- Po dokončení animace
    ClearPedTasks(playerPed)
    isCollecting = false
    interactedProps[prop] = true

    -- Cooldown pro prop
    Citizen.CreateThread(function()
        Citizen.Wait(Config.Cooldown * 1000)
        interactedProps[prop] = nil
    end)
end)
local inZone = false
local props = {}
local propPoints = {}
local cooldownProps = {}
local isCollecting = false

-- Vytvoření z<PERSON> pomocí ox_lib
local zone = lib.zones.sphere({
    coords = Config.Zone.coords,
    radius = Config.Zone.radius,
    debug = false,
    onEnter = function()
        inZone = true
        SpawnProps()
    end,
    onExit = function()
        inZone = false
        RemoveProps()
    end
})

-- Spawn propů
function SpawnProps()
    if not inZone then return end

    for i, coords in ipairs(Config.Prop.locations) do
        local prop = CreateObject(Config.Prop.model, coords.x, coords.y, coords.z, false, false, false)
        PlaceObjectOnGroundProperly(prop)
        FreezeEntityPosition(prop, true)
        SetEntityAsMissionEntity(prop, true, true)

        props[i] = prop

        -- Vytvoření interaction pointu pomocí ox_lib
        local point = lib.points.new({
            coords = coords,
            distance = 2.0,
            prop = prop,
            propIndex = i
        })

        function point:onEnter()
            if not inZone or isCollecting or cooldownProps[self.propIndex] then return end

            lib.showTextUI('[E] Sebrat tabák', {
                position = "top-center",
                icon = 'leaf',
                style = {
                    borderRadius = 0,
                    backgroundColor = '#48BB78',
                    color = 'white'
                }
            })
        end

        function point:onExit()
            lib.hideTextUI()
        end

        function point:nearby()
            if not inZone or isCollecting or cooldownProps[self.propIndex] then return end

            if IsControlJustReleased(0, 38) then -- E key
                if not ESX.PlayerData.job or ESX.PlayerData.job.name ~= Config.Job then
                    lib.notify({
                        title = 'Chyba',
                        description = Config.Notifications.noJob,
                        type = 'error'
                    })
                    return
                end

                TriggerServerEvent('redwood_tobacco:checkItemAndCollect', self.propIndex)
            end
        end

        propPoints[i] = point
    end
end

-- Odstranění propů
function RemoveProps()
    lib.hideTextUI()

    for i, prop in pairs(props) do
        if DoesEntityExist(prop) then
            DeleteObject(prop)
        end
    end

    for i, point in pairs(propPoints) do
        if point then
            point:remove()
        end
    end

    props = {}
    propPoints = {}
end

-- Animace sběru
RegisterNetEvent('redwood_tobacco:startCollecting')
AddEventHandler('redwood_tobacco:startCollecting', function(propIndex)
    isCollecting = true
    lib.hideTextUI()

    local playerPed = PlayerPedId()

    -- Načtení animace
    lib.requestAnimDict(Config.Animation.dict)

    -- Spuštění animace
    TaskPlayAnim(playerPed, Config.Animation.dict, Config.Animation.name, 8.0, -8.0, Config.Animation.duration, 0, 0, false, false, false)

    -- Progress bar s kontrolou dokončení
    local success = lib.progressBar({
        duration = Config.Animation.duration,
        label = 'Sbíráš tabák...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            move = true,
            car = true,
            combat = true,
            mouse = false
        },
        anim = {
            dict = Config.Animation.dict,
            clip = Config.Animation.name
        }
    })

    -- Po dokončení/zrušení
    ClearPedTasks(playerPed)
    isCollecting = false

    if success then
        -- Úspěšné dokončení - nastavení cooldownu
        cooldownProps[propIndex] = true

        -- Cooldown pro prop
        SetTimeout(Config.Cooldown * 1000, function()
            cooldownProps[propIndex] = nil
        end)

        TriggerServerEvent('redwood_tobacco:collectSuccess', propIndex)
    else
        -- Zrušeno
        lib.notify({
            title = 'Zrušeno',
            description = Config.Notifications.cancelled,
            type = 'error'
        })
    end
end)

-- Event pro úspěšný sběr
RegisterNetEvent('redwood_tobacco:collectComplete')
AddEventHandler('redwood_tobacco:collectComplete', function(amount, itemName)
    lib.notify({
        title = 'Úspěch',
        description = string.format(Config.Notifications.success, amount, itemName),
        type = 'success'
    })
end)
ESX = exports['es_extended']:getSharedObject()

-- Kontrola itemu a spuštění sběru
RegisterServerEvent('redwood_tobacco:checkItemAndCollect')
AddEventHandler('redwood_tobacco:checkItemAndCollect', function(prop)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)

    -- Kontrola jobu
    if xPlayer.job.name ~= Config.Job then
        xPlayer.showNotification('<PERSON><PERSON><PERSON><PERSON> být zaměstnán u Redwood!')
        return
    end

    -- Kontrola itemu v inventáři
    local hasItem = exports.ox_inventory:GetItemCount(source, Config.Item.required)
    if hasItem >= 1 then
        -- Přidání itemu
        exports.ox_inventory:AddItem(source, Config.Item.reward, Config.Item.amount)
        TriggerClientEvent('redwood_tobacco:startCollecting', source, prop)
        xPlayer.showNotification('Sebral jsi ' .. Config.Item.amount .. 'x ' .. Config.Item.reward .. '!')
    else
        xPlayer.showNotification('Po<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lopatu pro sběr tabáku!')
    end
end)
ESX = exports['es_extended']:getSharedObject()

-- Kontrola itemu a spuštění sběru
RegisterServerEvent('redwood_tobacco:checkItemAndCollect')
AddEventHandler('redwood_tobacco:checkItemAndCollect', function(propIndex)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then return end

    -- Kontrola jobu
    if xPlayer.job.name ~= Config.Job then
        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Chyba',
            description = Config.Notifications.noJob,
            type = 'error'
        })
        return
    end

    -- Kontrola itemu v inventáři
    local hasItem = exports.ox_inventory:GetItemCount(source, Config.Item.required)
    if hasItem >= 1 then
        -- Spuštění animace sběru
        TriggerClientEvent('redwood_tobacco:startCollecting', source, propIndex)
    else
        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Chyba',
            description = Config.Notifications.noItem,
            type = 'error'
        })
    end
end)

-- Úspěšné dokončení sběru
RegisterServerEvent('redwood_tobacco:collectSuccess')
AddEventHandler('redwood_tobacco:collectSuccess', function(propIndex)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then return end

    -- Dvojitá kontrola jobu a itemu
    if xPlayer.job.name ~= Config.Job then return end

    local hasItem = exports.ox_inventory:GetItemCount(source, Config.Item.required)
    if hasItem < 1 then return end

    -- Přidání itemu do inventáře
    local success = exports.ox_inventory:AddItem(source, Config.Item.reward, Config.Item.amount)

    if success then
        -- Notifikace o úspěchu
        TriggerClientEvent('redwood_tobacco:collectComplete', source, Config.Item.amount, Config.Item.reward)
    else
        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Chyba',
            description = 'Nemáš dostatek místa v inventáři!',
            type = 'error'
        })
    end
end)
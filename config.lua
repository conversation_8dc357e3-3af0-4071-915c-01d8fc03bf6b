Config = {}

-- <PERSON><PERSON><PERSON><PERSON> z<PERSON> pro sběr
Config.Zone = {
    coords = vector3(2854.0481, 4608.5562, 48.1356), -- <PERSON><PERSON><PERSON> zóny pro sběr
    radius = 50.0 -- <PERSON><PERSON><PERSON><PERSON>
}

-- <PERSON><PERSON><PERSON><PERSON> propů
Config.Prop = {
    model = `prop_plant_fern_02a`, -- Model propů (kapradina)
    locations = {
        vector3(2850.2, 4605.8, 48.1), -- Prop 1
        vector3(2857.1, 4610.2, 48.2), -- Prop 2
        vector3(2851.5, 4612.1, 48.0), -- Prop 3
        vector3(2860.3, 4607.4, 48.3), -- Prop 4
        vector3(2848.7, 4609.8, 47.9), -- Prop 5
        vector3(2855.9, 4605.1, 48.1), -- Prop 6
        vector3(2852.4, 4614.6, 48.2), -- Prop 7
        vector3(2858.8, 4612.9, 48.4), -- Prop 8
        -- <PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON>t da<PERSON><PERSON> souř<PERSON><PERSON>e podle potřeby
    }
}

-- Na<PERSON><PERSON><PERSON> itemů
Config.Item = {
    required = 'shovel', -- <PERSON><PERSON><PERSON>vaný item
    reward = 'tabakovy_list', -- Odměna
    amount = 1 -- Kolik itemů hráč dostane
}

-- Nastavení jobu
Config.Job = 'redwood'

-- Cooldown (v sekundách)
Config.Cooldown = 300 -- 5 minut

-- Nastavení animace a času
Config.Animation = {
    dict = 'melee@large_wpn@streamed_core',
    name = 'ground_attack_on_spot',
    duration = 5000 -- 5 sekund
}

-- Nastavení notifikací
Config.Notifications = {
    success = 'Úspěšně jsi sebral %dx %s!',
    noJob = 'Musíš být zaměstnán u Redwood!',
    noItem = 'Potřebuješ lopatu pro sběr tabáku!',
    cooldown = 'Tato rostlina byla nedávno sebrána, zkus to později!',
    cancelled = 'Sběr byl zrušen!'
}
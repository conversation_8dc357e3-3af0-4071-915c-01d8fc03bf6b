Config = {}

-- <PERSON><PERSON><PERSON><PERSON> zóny pro sběr
Config.Zone = {
    coords = vector3(1234.0, 5678.0, 10.0), -- <PERSON>raď souřadnicemi zóny
    radius = 50.0 -- <PERSON><PERSON><PERSON><PERSON>
}

-- Na<PERSON><PERSON>í propů
Config.Prop = {
    model = `prop_plant_fern_02a`, -- Model propů (kapradina)
    locations = {
        vector3(1234.5, 5678.5, 10.0), -- Nahraď souřadnicemi pro prop
        vector3(1235.0, 5679.0, 10.0), -- <PERSON><PERSON><PERSON> prop
        vector3(1233.0, 5677.5, 10.0), -- <PERSON><PERSON><PERSON> prop
        vector3(1236.0, 5678.8, 10.0), -- <PERSON><PERSON><PERSON> prop
        vector3(1232.5, 5679.2, 10.0), -- <PERSON><PERSON><PERSON> prop
        -- <PERSON><PERSON><PERSON>j dal<PERSON> souřadnice podle potřeby
    }
}

-- Nastavení itemů
Config.Item = {
    required = 'shovel', -- Požadovaný item
    reward = 'tabakovy_list', -- Odměna
    amount = 1 -- <PERSON><PERSON> itemů hráč dostane
}

-- Nastavení jobu
Config.Job = 'redwood'

-- Cooldown (v sekundách)
Config.Cooldown = 300 -- 5 minut

-- Nastavení animace a času
Config.Animation = {
    dict = 'melee@large_wpn@streamed_core',
    name = 'ground_attack_on_spot',
    duration = 5000 -- 5 sekund
}

-- Nastavení notifikací
Config.Notifications = {
    success = 'Úspěšně jsi sebral %dx %s!',
    noJob = 'Musíš být zaměstnán u Redwood!',
    noItem = 'Potřebuješ lopatu pro sběr tabáku!',
    cooldown = 'Tato rostlina byla nedávno sebrána, zkus to později!',
    cancelled = 'Sběr byl zrušen!'
}